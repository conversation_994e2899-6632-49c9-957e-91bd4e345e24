<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.ApiOrderMapper">
  <resultMap id="BaseResultMap" type="com.xy.admin.entity.ApiOrder">
    <!--@mbg.generated-->
    <!--@Table api_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="channel_no" jdbcType="VARCHAR" property="channelNo" />
    <result column="product_no" jdbcType="VARCHAR" property="productNo" />
    <result column="mobile_no" jdbcType="VARCHAR" property="mobileNo" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_status" jdbcType="VARCHAR" property="orderStatus" />
    <result column="out_order_status" jdbcType="VARCHAR" property="outOrderStatus" />
    <result column="out_order_id" jdbcType="VARCHAR" property="outOrderId" />
    <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
    <result column="client_ip" jdbcType="VARCHAR" property="clientIp" />
    <result column="user_agent" jdbcType="VARCHAR" property="userAgent" />
    <result column="app_package" jdbcType="VARCHAR" property="appPackage" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="page_url" jdbcType="VARCHAR" property="pageUrl" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="server" jdbcType="VARCHAR" property="server" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_id, channel_no, product_no, mobile_no, order_id, order_status, out_order_status, 
    out_order_id, operation_type, client_ip, user_agent, app_package, app_name, platform, 
    page_url, remark, create_time, update_time, server
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.xy.admin.entity.ApiOrder">
    <!--@mbg.generated-->
    update api_order
    set app_id = #{appId,jdbcType=VARCHAR},
      channel_no = #{channelNo,jdbcType=VARCHAR},
      product_no = #{productNo,jdbcType=VARCHAR},
      mobile_no = #{mobileNo,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=VARCHAR},
      out_order_status = #{outOrderStatus,jdbcType=VARCHAR},
      out_order_id = #{outOrderId,jdbcType=VARCHAR},
      operation_type = #{operationType,jdbcType=VARCHAR},
      client_ip = #{clientIp,jdbcType=VARCHAR},
      user_agent = #{userAgent,jdbcType=VARCHAR},
      app_package = #{appPackage,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      page_url = #{pageUrl,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      server = #{server,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 通用查询条件 -->
  <sql id="Common_Where_Clause">
    <where>
      <if test="query.startTime != null">
        AND create_time >= #{query.startTime}
      </if>
      <if test="query.endTime != null">
        AND create_time &lt;= #{query.endTime}
      </if>
      <if test="query.appIds != null and query.appIds.size() > 0">
        AND app_id IN
        <foreach collection="query.appIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="query.channelNos != null and query.channelNos.size() > 0">
        AND channel_no IN
        <foreach collection="query.channelNos" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="query.productNos != null and query.productNos.size() > 0">
        AND product_no IN
        <foreach collection="query.productNos" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="query.platforms != null and query.platforms.size() > 0">
        AND platform IN
        <foreach collection="query.platforms" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="query.operationTypes != null and query.operationTypes.size() > 0">
        AND operation_type IN
        <foreach collection="query.operationTypes" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="query.orderStatuses != null and query.orderStatuses.size() > 0">
        AND order_status IN
        <foreach collection="query.orderStatuses" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="query.outOrderStatuses != null and query.outOrderStatuses.size() > 0">
        AND out_order_status IN
        <foreach collection="query.outOrderStatuses" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="query.servers != null and query.servers.size() > 0">
        AND server IN
        <foreach collection="query.servers" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </sql>

  <!-- 概览统计查询 -->
  <select id="getOverviewStatistics" resultType="java.util.Map">
    SELECT
      COUNT(*) as total_count,
      COUNT(CASE WHEN order_status = '200' OR order_status = '0000' THEN 1 END) as success_count,
      COUNT(CASE WHEN order_status != '200' AND order_status != '0000' THEN 1 END) as fail_count,
      COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) as sms_count,
      COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) as order_count,
      COUNT(DISTINCT mobile_no) as unique_mobile_count,
      COUNT(DISTINCT client_ip) as unique_ip_count
    FROM api_order
    <include refid="Common_Where_Clause"/>
  </select>

  <!-- 趋势统计查询 -->
  <select id="getTrendStatistics" resultType="java.util.Map">
    SELECT
      <choose>
        <when test="query.timeDimension == 'hour'">
          DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00') as stat_time,
          DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00') as stat_time_str
        </when>
        <when test="query.timeDimension == 'day'">
          DATE_FORMAT(create_time, '%Y-%m-%d') as stat_time,
          DATE_FORMAT(create_time, '%Y-%m-%d') as stat_time_str
        </when>
        <when test="query.timeDimension == 'week'">
          DATE_FORMAT(create_time, '%Y-%u') as stat_time,
          CONCAT(YEAR(create_time), '年第', WEEK(create_time), '周') as stat_time_str
        </when>
        <when test="query.timeDimension == 'month'">
          DATE_FORMAT(create_time, '%Y-%m') as stat_time,
          DATE_FORMAT(create_time, '%Y年%m月') as stat_time_str
        </when>
        <otherwise>
          DATE_FORMAT(create_time, '%Y-%m-%d') as stat_time,
          DATE_FORMAT(create_time, '%Y-%m-%d') as stat_time_str
        </otherwise>
      </choose>,
      COUNT(*) as total_count,
      COUNT(CASE WHEN order_status = '200' OR order_status = '0000' THEN 1 END) as success_count,
      COUNT(CASE WHEN order_status != '200' AND order_status != '0000' THEN 1 END) as fail_count,
      COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) as sms_count,
      COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) as order_count,
      COUNT(DISTINCT mobile_no) as unique_mobile_count,
      COUNT(DISTINCT client_ip) as unique_ip_count
    FROM api_order
    <include refid="Common_Where_Clause"/>
    GROUP BY
      <choose>
        <when test="query.timeDimension == 'hour'">
          DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00')
        </when>
        <when test="query.timeDimension == 'day'">
          DATE_FORMAT(create_time, '%Y-%m-%d')
        </when>
        <when test="query.timeDimension == 'week'">
          DATE_FORMAT(create_time, '%Y-%u')
        </when>
        <when test="query.timeDimension == 'month'">
          DATE_FORMAT(create_time, '%Y-%m')
        </when>
        <otherwise>
          DATE_FORMAT(create_time, '%Y-%m-%d')
        </otherwise>
      </choose>
    ORDER BY stat_time
  </select>

  <!-- 排行榜统计查询 -->
  <select id="getRankingStatistics" resultType="java.util.Map">
    SELECT
      <if test="query.groupDimensions != null and query.groupDimensions.contains('app_id')">
        app_id,
      </if>
      <if test="query.groupDimensions != null and query.groupDimensions.contains('channel_no')">
        channel_no,
      </if>
      <if test="query.groupDimensions != null and query.groupDimensions.contains('product_no')">
        product_no,
      </if>
      <if test="query.groupDimensions != null and query.groupDimensions.contains('platform')">
        platform,
      </if>
      <if test="query.groupDimensions != null and query.groupDimensions.contains('operation_type')">
        operation_type,
      </if>
      <if test="query.groupDimensions != null and query.groupDimensions.contains('order_status')">
        order_status,
      </if>
      COUNT(*) as total_count,
      COUNT(CASE WHEN order_status = '200' OR order_status = '0000' THEN 1 END) as success_count,
      COUNT(CASE WHEN order_status != '200' AND order_status != '0000' THEN 1 END) as fail_count,
      COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) as sms_count,
      COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) as order_count,
      COUNT(DISTINCT mobile_no) as unique_mobile_count,
      COUNT(DISTINCT client_ip) as unique_ip_count
    FROM api_order
    <include refid="Common_Where_Clause"/>
    <if test="query.groupDimensions != null and query.groupDimensions.size() > 0">
      GROUP BY
      <foreach collection="query.groupDimensions" item="dimension" separator=",">
        ${dimension}
      </foreach>
    </if>
    ORDER BY
    <choose>
      <when test="query.orderBy == 'success_count'">
        success_count
      </when>
      <when test="query.orderBy == 'fail_count'">
        fail_count
      </when>
      <when test="query.orderBy == 'success_rate'">
        (success_count * 100.0 / NULLIF(total_count, 0))
      </when>
      <otherwise>
        total_count
      </otherwise>
    </choose>
    <choose>
      <when test="query.orderDirection == 'asc'">
        ASC
      </when>
      <otherwise>
        DESC
      </otherwise>
    </choose>
    <if test="query.limit != null and query.limit > 0">
      LIMIT #{query.limit}
    </if>
  </select>

  <!-- 多维度统计分析查询 -->
  <select id="statisticsAnalysis" resultType="java.util.Map">
    SELECT
      <choose>
        <when test="query.timeDimension != null">
          <choose>
            <when test="query.timeDimension == 'hour'">
              DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00') as stat_time,
              DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00') as stat_time_str,
            </when>
            <when test="query.timeDimension == 'day'">
              DATE_FORMAT(create_time, '%Y-%m-%d') as stat_time,
              DATE_FORMAT(create_time, '%Y-%m-%d') as stat_time_str,
            </when>
            <when test="query.timeDimension == 'week'">
              DATE_FORMAT(create_time, '%Y-%u') as stat_time,
              CONCAT(YEAR(create_time), '年第', WEEK(create_time), '周') as stat_time_str,
            </when>
            <when test="query.timeDimension == 'month'">
              DATE_FORMAT(create_time, '%Y-%m') as stat_time,
              DATE_FORMAT(create_time, '%Y年%m月') as stat_time_str,
            </when>
            <otherwise>
              DATE_FORMAT(create_time, '%Y-%m-%d') as stat_time,
              DATE_FORMAT(create_time, '%Y-%m-%d') as stat_time_str,
            </otherwise>
          </choose>
        </when>
      </choose>
      <if test="query.groupDimensions != null and query.groupDimensions.contains('app_id')">
        app_id,
      </if>
      <if test="query.groupDimensions != null and query.groupDimensions.contains('channel_no')">
        channel_no,
      </if>
      <if test="query.groupDimensions != null and query.groupDimensions.contains('product_no')">
        product_no,
      </if>
      <if test="query.groupDimensions != null and query.groupDimensions.contains('platform')">
        platform,
      </if>
      <if test="query.groupDimensions != null and query.groupDimensions.contains('operation_type')">
        operation_type,
      </if>
      <if test="query.groupDimensions != null and query.groupDimensions.contains('order_status')">
        order_status,
      </if>
      COUNT(*) as total_count,
      COUNT(CASE WHEN order_status = '200' OR order_status = '0000' THEN 1 END) as success_count,
      COUNT(CASE WHEN order_status != '200' AND order_status != '0000' THEN 1 END) as fail_count,
      COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) as sms_count,
      COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) as order_count,
      COUNT(DISTINCT mobile_no) as unique_mobile_count,
      COUNT(DISTINCT client_ip) as unique_ip_count
    FROM api_order
    <include refid="Common_Where_Clause"/>
    <if test="(query.timeDimension != null) or (query.groupDimensions != null and query.groupDimensions.size() > 0)">
      GROUP BY
      <if test="query.timeDimension != null">
        <choose>
          <when test="query.timeDimension == 'hour'">
            DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00')
          </when>
          <when test="query.timeDimension == 'day'">
            DATE_FORMAT(create_time, '%Y-%m-%d')
          </when>
          <when test="query.timeDimension == 'week'">
            DATE_FORMAT(create_time, '%Y-%u')
          </when>
          <when test="query.timeDimension == 'month'">
            DATE_FORMAT(create_time, '%Y-%m')
          </when>
          <otherwise>
            DATE_FORMAT(create_time, '%Y-%m-%d')
          </otherwise>
        </choose>
        <if test="query.groupDimensions != null and query.groupDimensions.size() > 0">,</if>
      </if>
      <if test="query.groupDimensions != null and query.groupDimensions.size() > 0">
        <foreach collection="query.groupDimensions" item="dimension" separator=",">
          ${dimension}
        </foreach>
      </if>
    </if>
    ORDER BY
    <if test="query.timeDimension != null">
      stat_time,
    </if>
    <choose>
      <when test="query.orderBy == 'success_count'">
        success_count
      </when>
      <when test="query.orderBy == 'fail_count'">
        fail_count
      </when>
      <when test="query.orderBy == 'success_rate'">
        (success_count * 100.0 / NULLIF(total_count, 0))
      </when>
      <otherwise>
        total_count
      </otherwise>
    </choose>
    <choose>
      <when test="query.orderDirection == 'asc'">
        ASC
      </when>
      <otherwise>
        DESC
      </otherwise>
    </choose>
    <if test="query.limit != null and query.limit > 0">
      LIMIT #{query.limit}
    </if>
  </select>

</mapper>