package com.xy.admin.vo.apiOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * API订单统计结果VO
 * <AUTHOR>
 * @since 2025/7/30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiOrderStatResultVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 统计时间段
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime statTime;
    
    /**
     * 统计时间段（字符串格式，便于前端展示）
     */
    private String statTimeStr;
    
    /**
     * 分组维度值（key为维度名，value为维度值）
     */
    private Map<String, String> groupValues;
    
    /**
     * 总订单数
     */
    private Long totalCount = 0L;
    
    /**
     * 成功订单数（订单状态为成功的）
     */
    private Long successCount = 0L;
    
    /**
     * 失败订单数
     */
    private Long failCount = 0L;
    
    /**
     * 成功率（百分比，保留2位小数）
     */
    private BigDecimal successRate = BigDecimal.ZERO;
    
    /**
     * 失败率（百分比，保留2位小数）
     */
    private BigDecimal failRate = BigDecimal.ZERO;
    
    /**
     * SMS操作数量
     */
    private Long smsCount = 0L;
    
    /**
     * ORDER操作数量
     */
    private Long orderCount = 0L;
    
    /**
     * 转化率（ORDER/SMS，百分比，保留2位小数）
     */
    private BigDecimal conversionRate = BigDecimal.ZERO;
    
    /**
     * 独立手机号数量
     */
    private Long uniqueMobileCount = 0L;
    
    /**
     * 独立IP数量
     */
    private Long uniqueIpCount = 0L;
    
    /**
     * 详细统计数据（当includeDetails=true时返回）
     */
    private List<ApiOrderStatDetailVO> details;
    
    /**
     * 统计详细信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ApiOrderStatDetailVO implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 应用ID
         */
        private String appId;
        
        /**
         * 渠道编号
         */
        private String channelNo;
        
        /**
         * 产品编号
         */
        private String productNo;
        
        /**
         * 平台
         */
        private String platform;
        
        /**
         * 操作类型
         */
        private String operationType;
        
        /**
         * 订单状态
         */
        private String orderStatus;
        
        /**
         * 外部订单状态
         */
        private String outOrderStatus;
        
        /**
         * 服务器
         */
        private String server;
        
        /**
         * 数量
         */
        private Long count;
        
        /**
         * 占比（百分比，保留2位小数）
         */
        private BigDecimal percentage;
    }
}
