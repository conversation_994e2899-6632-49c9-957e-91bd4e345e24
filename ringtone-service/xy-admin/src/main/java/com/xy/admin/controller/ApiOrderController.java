package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.annotation.DataSource;
import com.xy.admin.service.ApiOrderService;
import com.xy.admin.vo.apiOrder.ApiOrderQueryVO;
import com.xy.admin.vo.apiOrder.ApiOrderStatQueryVO;
import com.xy.admin.vo.apiOrder.ApiOrderStatResultVO;
import com.xy.admin.vo.apiOrder.ApiOrderVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
* 营销推广订单综合表(api_order)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/api_order")
public class ApiOrderController {

    @Autowired
    private ApiOrderService apiOrderService;

    /**
     * 列表查询 - 支持多字段查询、分页和排序
     */
    @PostMapping("/list")
    @DataSource("tertiary")
    public IPage<ApiOrderVO> list(@Valid @RequestBody ApiOrderQueryVO queryVO) {
        return apiOrderService.queryPage(queryVO);
    }

    /**
     * 多维度统计分析
     */
    @PostMapping("/statistics/analysis")
    @DataSource("tertiary")
    public List<ApiOrderStatResultVO> statisticsAnalysis(@Valid @RequestBody ApiOrderStatQueryVO queryVO) {
        return apiOrderService.statisticsAnalysis(queryVO);
    }

    /**
     * 获取概览统计数据
     */
    @PostMapping("/statistics/overview")
    @DataSource("tertiary")
    public ApiOrderStatResultVO getOverviewStatistics(@Valid @RequestBody ApiOrderStatQueryVO queryVO) {
        return apiOrderService.getOverviewStatistics(queryVO);
    }

    /**
     * 获取趋势统计数据
     */
    @PostMapping("/statistics/trend")
    @DataSource("tertiary")
    public List<ApiOrderStatResultVO> getTrendStatistics(@Valid @RequestBody ApiOrderStatQueryVO queryVO) {
        return apiOrderService.getTrendStatistics(queryVO);
    }

    /**
     * 获取排行榜数据
     */
    @PostMapping("/statistics/ranking")
    @DataSource("tertiary")
    public List<ApiOrderStatResultVO> getRankingStatistics(@Valid @RequestBody ApiOrderStatQueryVO queryVO) {
        return apiOrderService.getRankingStatistics(queryVO);
    }
}
