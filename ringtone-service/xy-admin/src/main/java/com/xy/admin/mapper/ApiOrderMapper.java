package com.xy.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xy.admin.entity.ApiOrder;
import com.xy.admin.vo.apiOrder.ApiOrderStatQueryVO;
import com.xy.admin.vo.apiOrder.ApiOrderStatResultVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2025/7/30 15:39
 */
@Mapper
public interface ApiOrderMapper extends BaseMapper<ApiOrder> {

    /**
     * 多维度统计分析
     * @param queryVO 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> statisticsAnalysis(@Param("query") ApiOrderStatQueryVO queryVO);

    /**
     * 获取概览统计数据
     * @param queryVO 查询条件
     * @return 概览统计结果
     */
    Map<String, Object> getOverviewStatistics(@Param("query") ApiOrderStatQueryVO queryVO);

    /**
     * 获取趋势统计数据
     * @param queryVO 查询条件
     * @return 趋势统计结果
     */
    List<Map<String, Object>> getTrendStatistics(@Param("query") ApiOrderStatQueryVO queryVO);

    /**
     * 获取排行榜数据
     * @param queryVO 查询条件
     * @return 排行榜结果
     */
    List<Map<String, Object>> getRankingStatistics(@Param("query") ApiOrderStatQueryVO queryVO);

}