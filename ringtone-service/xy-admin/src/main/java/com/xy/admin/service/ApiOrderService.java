package com.xy.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.entity.ApiOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.admin.vo.apiOrder.ApiOrderQueryVO;
import com.xy.admin.vo.apiOrder.ApiOrderStatQueryVO;
import com.xy.admin.vo.apiOrder.ApiOrderStatResultVO;
import com.xy.admin.vo.apiOrder.ApiOrderVO;

import java.util.List;

/**
 * 营销推广订单综合表(api_order)表服务接口
 *
 * <AUTHOR>
 * @since 2025/7/30 15:39
 */
public interface ApiOrderService extends IService<ApiOrder>{

    /**
     * 多字段分页查询
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<ApiOrderVO> queryPage(ApiOrderQueryVO queryVO);

    /**
     * 多维度统计分析
     * @param queryVO 统计查询条件
     * @return 统计结果列表
     */
    List<ApiOrderStatResultVO> statisticsAnalysis(ApiOrderStatQueryVO queryVO);

    /**
     * 获取概览统计数据
     * @param queryVO 统计查询条件
     * @return 概览统计结果
     */
    ApiOrderStatResultVO getOverviewStatistics(ApiOrderStatQueryVO queryVO);

    /**
     * 获取趋势统计数据
     * @param queryVO 统计查询条件
     * @return 趋势统计结果列表
     */
    List<ApiOrderStatResultVO> getTrendStatistics(ApiOrderStatQueryVO queryVO);

    /**
     * 获取排行榜数据
     * @param queryVO 统计查询条件
     * @return 排行榜结果列表
     */
    List<ApiOrderStatResultVO> getRankingStatistics(ApiOrderStatQueryVO queryVO);

}
