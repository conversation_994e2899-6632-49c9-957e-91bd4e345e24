package com.xy.admin.vo.apiOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API订单统计查询VO
 * <AUTHOR>
 * @since 2025/7/30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiOrderStatQueryVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 统计开始时间（必填）
     */
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /**
     * 统计结束时间（必填）
     */
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /**
     * 时间维度：day-按天，hour-按小时，week-按周，month-按月
     */
    private String timeDimension = "day";
    
    /**
     * 分组维度：可多选
     * app_id-应用ID, channel_no-渠道编号, product_no-产品编号, 
     * platform-平台, operation_type-操作类型, order_status-订单状态
     */
    private List<String> groupDimensions;
    
    /**
     * 应用ID过滤
     */
    private List<String> appIds;
    
    /**
     * 渠道编号过滤
     */
    private List<String> channelNos;
    
    /**
     * 产品编号过滤
     */
    private List<String> productNos;
    
    /**
     * 平台过滤
     */
    private List<String> platforms;
    
    /**
     * 操作类型过滤：SMS-短信发送，ORDER-订单提交
     */
    private List<String> operationTypes;
    
    /**
     * 订单状态过滤
     */
    private List<String> orderStatuses;
    
    /**
     * 外部订单状态过滤
     */
    private List<String> outOrderStatuses;
    
    /**
     * 服务器过滤
     */
    private List<String> servers;
    
    /**
     * 是否包含详细数据（默认false，只返回汇总）
     */
    private Boolean includeDetails = false;
    
    /**
     * 排序字段：total_count-总数, success_count-成功数, fail_count-失败数, success_rate-成功率
     */
    private String orderBy = "total_count";
    
    /**
     * 排序方向：asc-升序, desc-降序
     */
    private String orderDirection = "desc";
    
    /**
     * 限制返回条数（用于排行榜）
     */
    private Integer limit;
}
